import React, { useState, useRef, useEffect } from "react";
import { Button } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { MessageHistory } from "./AvatarSession/MessageHistory";
import { useMessageHistory, MessageSender } from "./logic";
import { useTextChat } from "./logic/useTextChat";
import { StreamingAvatarSessionState } from "./logic";
import { useStreamingAvatarSession } from "./logic/useStreamingAvatarSession";
import clsx from "clsx";

interface FloatingChatInterfaceProps {
  sessionState: StreamingAvatarSessionState;
}

export const FloatingChatInterface: React.FC<FloatingChatInterfaceProps> = ({
  sessionState,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [message, setMessage] = useState("");
  const { sendMessage } = useTextChat();
  const { messages } = useMessageHistory();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (
      message.trim() &&
      sessionState === StreamingAvatarSessionState.CONNECTED
    ) {
      sendMessage(message);
      setMessage("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSendMessage();
    }
  };

  const toggleChat = () => {
    setIsExpanded(!isExpanded);
  };

  if (!isExpanded) {
    // Floating button when collapsed
    return (
      <div
        className="fixed"
        style={{
          bottom: "2rem",
          right: "2rem",
          zIndex: 1000,
        }}
      >
        <Button
          onClick={toggleChat}
          className="p-button-rounded p-button-lg"
          style={{
            width: "60px",
            height: "60px",
            backgroundColor: "#515151",
            border: "none",
            boxShadow: "0 4px 12px rgba(0, 0, 0, 0.15)",
            transition: "all 0.3s ease",
            animation: "bounceIn 0.6s ease-out",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = "scale(1.1)";
            e.currentTarget.style.backgroundColor = "#1B84FF";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = "scale(1)";
            e.currentTarget.style.backgroundColor = "#515151";
          }}
        >
          <i
            className="pi pi-comments"
            style={{ fontSize: "1.5rem", color: "white" }}
          />
          {messages.length > 0 && (
            <div
              className="absolute"
              style={{
                top: "-5px",
                right: "-5px",
                backgroundColor: "#1B84FF",
                color: "white",
                borderRadius: "50%",
                width: "20px",
                height: "20px",
                fontSize: "12px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                fontWeight: "bold",
              }}
            >
              {messages.length > 9 ? "9+" : messages.length}
            </div>
          )}
        </Button>
      </div>
    );
  }

  // Expanded chat interface
  return (
    <div
      className="fixed"
      style={{
        bottom: "2rem",
        right: "2rem",
        width: "400px",
        height: "600px",
        backgroundColor: "#fff",
        border: "1px solid #5151511a",
        borderRadius: "20px",
        boxShadow: "0 10px 30px rgba(0, 0, 0, 0.15)",
        zIndex: 1000,
        display: "flex",
        flexDirection: "column",
        overflow: "hidden",
        animation: "slideInUp 0.3s ease-out",
      }}
    >
      {/* Header */}
      <div
        className="flex align-items-center justify-content-between"
        style={{
          padding: "1rem 1.5rem",
          borderBottom: "1px solid #5151511a",
          backgroundColor: "#f8f9fa",
          borderRadius: "20px 20px 0 0",
        }}
      >
        <div className="flex align-items-center" style={{ gap: "0.5rem" }}>
          <i
            className="pi pi-comments"
            style={{ color: "#515151", fontSize: "1.2rem" }}
          />
          <h3
            style={{
              color: "#515151",
              margin: 0,
              fontSize: "1.1rem",
              fontWeight: "600",
            }}
          >
            Chat Assistant
          </h3>
        </div>
        <div className="flex align-items-center" style={{ gap: "0.5rem" }}>
          {/* Status indicator */}
          <div
            className="w-2 h-2 border-round-full"
            style={{
              backgroundColor:
                sessionState === StreamingAvatarSessionState.CONNECTED
                  ? "#4caf50"
                  : sessionState === StreamingAvatarSessionState.INACTIVE
                  ? "#bdbdbd"
                  : "#ff9800",
            }}
          />
          <Button
            onClick={toggleChat}
            className="p-button-text p-button-sm"
            style={{
              color: "#515151",
              padding: "0.25rem",
              minWidth: "auto",
              width: "2rem",
              height: "2rem",
            }}
          >
            <i className="pi pi-times" />
          </Button>
        </div>
      </div>

      {/* Messages Area */}
      <div
        className="flex-1 overflow-y-auto"
        style={{
          padding: "1rem",
          backgroundColor: "#fff",
        }}
      >
        {sessionState !== StreamingAvatarSessionState.CONNECTED ? (
          <div
            className="flex flex-column align-items-center justify-content-center h-full"
            style={{ gap: "1rem", color: "#515151" }}
          >
            <i
              className="pi pi-info-circle"
              style={{ fontSize: "2rem", color: "#bdbdbd" }}
            />
            <p style={{ textAlign: "center", margin: 0 }}>
              {sessionState === StreamingAvatarSessionState.INACTIVE
                ? "Start a conversation with your avatar to begin chatting"
                : "Connecting to avatar..."}
            </p>
          </div>
        ) : messages.length === 0 ? (
          <div
            className="flex flex-column align-items-center justify-content-center h-full"
            style={{ gap: "1rem", color: "#515151" }}
          >
            <i
              className="pi pi-comments"
              style={{ fontSize: "2rem", color: "#bdbdbd" }}
            />
            <p style={{ textAlign: "center", margin: 0 }}>
              Start your conversation here
            </p>
          </div>
        ) : (
          <div
            style={{ display: "flex", flexDirection: "column", gap: "1rem" }}
          >
            {messages.map((msg, index) => (
              <div
                key={msg.id}
                className={`flex ${
                  msg.sender === MessageSender.CLIENT
                    ? "justify-content-end"
                    : "justify-content-start"
                }`}
                style={{
                  animation: `fadeIn 0.3s ease-in-out ${index * 0.1}s both`,
                }}
              >
                <div
                  style={{
                    maxWidth: "80%",
                    padding: "0.75rem 1rem",
                    borderRadius: "18px",
                    backgroundColor:
                      msg.sender === MessageSender.CLIENT
                        ? "#5151511A"
                        : "#1B84FF1A",
                    color: "#515151",
                    fontSize: "0.9rem",
                    lineHeight: "1.4",
                    wordBreak: "break-word",
                  }}
                >
                  {msg.content}
                </div>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Input Area */}
      <div
        style={{
          padding: "1rem",
          borderTop: "1px solid #5151511a",
          backgroundColor: "#f8f9fa",
          borderRadius: "0 0 20px 20px",
        }}
      >
        <div className="flex" style={{ gap: "0.5rem" }}>
          <InputText
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={
              sessionState === StreamingAvatarSessionState.CONNECTED
                ? "Type your message..."
                : "Connect to avatar first"
            }
            disabled={sessionState !== StreamingAvatarSessionState.CONNECTED}
            style={{
              flex: 1,
              border: "1px solid #5151511a",
              borderRadius: "25px",
              padding: "0.75rem 1rem",
              fontSize: "0.9rem",
              backgroundColor: "#fff",
            }}
          />
          <Button
            onClick={handleSendMessage}
            disabled={
              !message.trim() ||
              sessionState !== StreamingAvatarSessionState.CONNECTED
            }
            className="p-button-rounded"
            style={{
              backgroundColor:
                message.trim() &&
                sessionState === StreamingAvatarSessionState.CONNECTED
                  ? "#515151"
                  : "#bdbdbd",
              border: "none",
              width: "40px",
              height: "40px",
              minWidth: "40px",
            }}
          >
            <i className="pi pi-send" style={{ fontSize: "0.9rem" }} />
          </Button>
        </div>
      </div>
    </div>
  );
};
